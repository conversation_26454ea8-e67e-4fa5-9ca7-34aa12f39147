import rclpy
from rclpy.node import Node
from std_msgs.msg import Float32MultiArray, UInt8
import sys
import termios
import tty
import pygame


class TeleopNode(Node):
    def __init__(self):
        super().__init__("amr_teleop_node")

        # Publishers
        self.pose_publisher = self.create_publisher(
            Float32MultiArray, "amr/wheel_pose_control", 10
        )
        self.emergency_publisher = self.create_publisher(UInt8, "amr/power", 10)

        # State Variables
        self.travel_speed_fw = 0
        self.travel_speed_rl = 0
        self.travel_speed_rr = 0
        self.steer_angle = 0.0
        self.max_speed = 0.0
        self.joystick_connected = False
        self.current_deadman_switch_pressed = 0
        self.previous_deadman_switch_pressed = 0
        self.SPEED_INCREMENT = 0.01
        self.ANGLE_INCREMENT = 1.0
        self.ABS_MAX_SPEED = 0.1
        self.MAX_ANGLE = 130.0
        self.MIN_ANGLE = -130.0

        # Joystick offsets (correcting inaccuracies)
        self.JOYSTICK_AXIS_1_OFFSET = 0.00390625
        self.JOYSTICK_AXIS_2_OFFSET = 0.003936767578125

        # Initialize joystick if available
        pygame.init()
        if pygame.joystick.get_count() > 0:
            self.joystick = pygame.joystick.Joystick(0)
            self.joystick.init()
            self.joystick_connected = True
            self.get_logger().info("Joystick detected, switching to joystick control.")

        self.get_logger().info(
            "Teleoperation started. Use W/A/S/D/X keys or joystick to control."
        )

        # Start listening for inputs
        self.listen_for_inputs()

    def get_key(self):
        """Reads keyboard input."""
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        try:
            tty.setraw(sys.stdin.fileno())
            key = sys.stdin.read(1)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        return key

    def listen_for_inputs(self):
        """Handles joystick or keyboard input continuously."""
        try:
            while rclpy.ok():
                if self.joystick_connected:
                    self.process_joystick_input()
                else:
                    self.current_deadman_switch_pressed = 1
                    self.previous_deadman_switch_pressed = 0
                    self.process_keyboard_input()

                if self.current_deadman_switch_pressed:
                    self.publish_pose_control()
                elif self.previous_deadman_switch_pressed:
                    self.trigger_emergency_stop()

                rclpy.spin_once(self, timeout_sec=0.1)
        except KeyboardInterrupt:
            self.get_logger().info("Exiting teleoperation...")
            self.destroy_node()
            rclpy.shutdown()
            sys.exit(0)

    def process_joystick_input(self):
        """Processes joystick inputs and updates movement values."""
        pygame.event.pump()
        self.current_deadman_switch_pressed = self.joystick.get_button(6)

        if self.current_deadman_switch_pressed:
            self.update_travel_speed()
            self.update_steering_angle()
            self.update_speed_limits()
        elif self.previous_deadman_switch_pressed:
            self.trigger_emergency_stop()

    def process_keyboard_input(self):
        """Handles keyboard inputs for movement and emergency stop."""
        key = self.get_key()

        if key == "w":
            self.max_speed = min(
                self.max_speed + self.SPEED_INCREMENT, self.ABS_MAX_SPEED
            )
            self.travel_speed_fw = min(
                self.travel_speed_fw + self.SPEED_INCREMENT, self.max_speed
            )
            self.travel_speed_rl = self.travel_speed_fw
            self.travel_speed_rr = self.travel_speed_fw
            self.get_logger().info(f"Max Speed Increased: {self.max_speed}")

        elif key == "x":
            self.travel_speed_fw = max(
                self.travel_speed_fw - self.SPEED_INCREMENT, -self.max_speed
            )
            self.travel_speed_rl = self.travel_speed_fw
            self.travel_speed_rr = self.travel_speed_fw

        elif key == "a":
            self.steer_angle = max(
                self.steer_angle - self.ANGLE_INCREMENT, self.MIN_ANGLE
            )
        elif key == "d":
            self.steer_angle = min(
                self.steer_angle + self.ANGLE_INCREMENT, self.MAX_ANGLE
            )
        elif key == "s":
            self.trigger_emergency_stop()
        elif key == "\x03":  # Ctrl+C
            raise KeyboardInterrupt

    def update_travel_speed(self):
        """Handles joystick axis movement for travel speed."""
        axis_1 = self.joystick.get_axis(1) - self.JOYSTICK_AXIS_1_OFFSET
        axis_2 = self.joystick.get_axis(2) + self.JOYSTICK_AXIS_2_OFFSET

        if axis_1 != 0:
            self.set_travel_speed(-axis_1 * self.max_speed)
        elif axis_2 != 0:
            self.set_side_travel_speed(axis_2 * self.max_speed)
        else:
            self.reset_travel_speed()

    def set_travel_speed(self, speed):
        """Sets travel speed while respecting limits."""
        speed = max(min(speed, self.max_speed), -self.max_speed)
        self.travel_speed_fw = speed
        self.travel_speed_rl = speed
        self.travel_speed_rr = speed

    def set_side_travel_speed(self, speed):
        """Handles side movement travel speeds with max limit `ABS_MAX_SPEED`."""
        speed = max(min(speed, self.ABS_MAX_SPEED), -self.ABS_MAX_SPEED)
        self.travel_speed_fw = speed
        self.travel_speed_rl = -speed
        self.travel_speed_rr = -speed

    def reset_travel_speed(self):
        """Resets travel speed when no movement is detected."""
        self.travel_speed_fw = 0.0
        self.travel_speed_rl = 0.0
        self.travel_speed_rr = 0.0

    def publish_pose_control(self):
        """Publishes speed and steering data to ROS topics."""
        msg = Float32MultiArray()
        msg.data = [
            self.travel_speed_fw,
            self.steer_angle,  # Front Wheel
            self.travel_speed_rl,
            self.steer_angle,  # Rear Left Wheel
            self.travel_speed_rr,
            self.steer_angle,  # Rear Right Wheel
        ]
        self.pose_publisher.publish(msg)
        self.get_logger().info(
            f"Speed: {self.travel_speed_fw}/{self.max_speed} | Angle: {self.steer_angle}"
        )

    def trigger_emergency_stop(self):
        """Triggers an emergency stop when the deadman switch is released."""
        self.travel_speed_fw = 0.0
        self.travel_speed_rl = 0.0
        self.travel_speed_rr = 0.0
        self.publish_emergency()
        self.previous_deadman_switch_pressed = 0  # Reset switch state after stopping

    def publish_emergency(self):
        """Publishes an emergency stop signal to ROS."""
        msg = UInt8()
        msg.data = 1  # Emergency stop signal
        self.emergency_publisher.publish(msg)
        self.get_logger().warn("Emergency Stop Activated!")


def main():
    rclpy.init()
    node = TeleopNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()

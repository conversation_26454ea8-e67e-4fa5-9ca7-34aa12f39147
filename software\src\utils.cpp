#include "utils.hpp"
#include <stdarg.h>
#include <stdint.h>

float interpret_can_bytes_to_decimal_big_endian(const float resolution,
                                                const float offset,
                                                const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * idx);
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}
float interpret_can_bytes_to_decimal_little_endian(const float resolution,
                                                   const float offset,
                                                   const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * (count - idx - 1));
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}

// Daly BMS Provides offsets as negative numbers
// The offset needs to be subtracted before the resolution is multiplied
float interpret_can_bytes_to_decimal_little_endian_daly_bms(
    const float resolution, const float offset, const int count, ...) {
  va_list args;

  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    const uint8_t hex_val = va_arg(args, int);
    hexValue |= hex_val << (8 * (count - idx - 1));
  }
  float converted_value = (float)hexValue;
  converted_value -= offset;
  converted_value = converted_value * resolution;
  return converted_value;
}

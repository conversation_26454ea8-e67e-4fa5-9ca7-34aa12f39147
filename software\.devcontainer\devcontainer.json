{
	"runArgs": ["--privileged"],
	"build": {
		"dockerfile": "Dockerfile"
	},
	"customizations": {
		"vscode": {
			"settings": {
				"terminal.integrated.shell.linux": "/bin/bash"  // Or your preferred shell
			},
			"extensions": [
				"ms-vscode.cpptools"
			]
		}
    },
	"mounts": [
		{
			"type": "bind",
			"source": "/dev",
			"target": "/dev"
		}
		,
		{
			"type": "bind",
			"source": "${localEnv:SSH_AUTH_SOCK}",
			"target": "${localEnv:SSH_AUTH_SOCK}"
		}
		,
		{
			"type": "bind",
			"source": "${localEnv:HOME}/.ssh",
			"target": "/home/<USER>/.ssh"
		}
	],
// See https://javorszky.co.uk/2023/11/02/use-your-ssh-key-with-a-passphrase-inside-a-docker-container/
	"containerEnv":{
		"SSH_AUTH_SOCK": "${localEnv:SSH_AUTH_SOCK}"
	}
	,
	"postAttachCommand": "sudo service udev restart"
}


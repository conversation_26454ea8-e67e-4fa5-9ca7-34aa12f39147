#include <Arduino.h>

// C++ Standard Libraries
#include <cstdarg>
#include <cstddef>
#include <cstdint>

// External Libraries for peripherals
#include "ACAN_T4_CANMessage.h"
#include "Print.h"
#include "core_pins.h"
#include "dev_kit_error.hpp"
#include "pins_arduino.h"
#include "rcb_pinout.hpp"
#include <ACAN_T4.h>
#include <NativeEthernet.h>
#include <SPI.h>
#include <micro_ros_platformio.h>

// uROS Libraries
#include "rcl/publisher.h"
#include "rcl/subscription.h"
#include <rcl/error_handling.h>
#include <rcl/rcl.h>
#include <rclc/executor.h>
#include <rclc/rclc.h>
#include <rmw_microros/rmw_microros.h>

#include <std_msgs/msg/float32.h>
#include <std_msgs/msg/float32_multi_array.h>
#include <std_msgs/msg/int32.h>
#include <std_msgs/msg/int32_multi_array.h>
#include <std_msgs/msg/u_int16.h>
#include <std_msgs/msg/u_int16_multi_array.h>
#include <std_msgs/msg/u_int8.h>

// Custom Headers
#include "battery.hpp"
#include "config.hpp"
#include "object_dictionary.hpp"
#include "ros_comms.hpp"
#include "tower_light.hpp"
#include "uros.hpp"
#include "utils.hpp"
#include "wheel_control.hpp"

#ifndef __IMXRT1062__
#endif

#ifdef TESTING_MODE
#define MODE "Testing Mode"
#elif DEBUG
#define MODE "Debug Mode"
#else
#define MODE "Release Mode"
#endif

// ROS Configurations
#define NODE_NAME "amr_control_board_node"
#define AMR_SET_STATE_TOPIC "amr/set_state"
#define AMR_GET_STATE_TOPIC "amr/get_state"
#define RAW_ENCODER_PUBLISHER_TOPIC "amr/raw_encoder"
#define MOTOR_ERROR_PUBLISHER_TOPIC "amr/motor_error"
#define REQUIRED_TRAVEL_MOTOR_1_SPEED_SUBSCRIBER_TOPIC                         \
  "amr/required_travel_motor_speed"
#define REQUIRED_STEER_MOTOR_1_ANGLE_SUBSCRIBER_TOPIC                          \
  "amr/required_steer_motor_angle"
#define WHEEL_POSE_CONTROL_TOPIC "amr/wheel_pose_control"
#define AMR_POWER_TOPIC "amr/power"
#define POWER_SYSTEM_ACTUATION_BATTERY_STATUS_TOPIC                            \
  "power_system/get_actuation_battery_readings"
#define HEARTBEAT_TOPIC "heartbeat"

#define RCCHECK(fn)                                                            \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
      error_loop();                                                            \
    }                                                                          \
  }
#define RCSOFTCHECK(fn)                                                        \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
    }                                                                          \
  }

// Wheel Parameters
#define FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x01
#define FRONT_WHEEL_STEER_MOTOR_NODE_ID 0x02
#define REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x03
#define REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID 0x04
#define REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x05
#define REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID 0x06

// Frequencies for communications (all times are in mS)
#define ERROR_TIME 100            // 2Hz
#define ENCODER_TIME 2            // 500Hz
#define BATTERY_TIME 10 * 1000    // 0.1Hz
#define HEARTBEAT_TIME_MS 1000    // 1Hz
#define AMR_GET_STATE_TIMER_MS 50 // 1Hz

#define TOTAL_PUB_SUB_HANDLES 50
// ROS Publishers
MicroROS_Publisher_t raw_encoder_value_publisher;        // Int32 multiarray
MicroROS_Publisher_t motor_error_publisher;              // Uint16 multiarray
MicroROS_Publisher_t pub_get_actuation_battery_readings; // Uint8 multiarray
MicroROS_Publisher_t pub_heartbeat;                      // Uint8
MicroROS_Publisher_t pub_amr_get_state;                  // Uint8

// ROS Subscribers
MicroROS_Subscriber_t wheel_pose_control_subscriber; // Float32 MultiArray
MicroROS_Subscriber_t amr_motor_power_subscriber;
MicroROS_Subscriber_t sub_heartbeat;     // Uint8
MicroROS_Subscriber_t sub_amr_set_state; // Uint8

// ROS Datatypes
bool wheel_pose_control_msg_recieved = false;
bool flag_state_change_requested = false;
std_msgs__msg__Float32MultiArray wheel_pose_control_msg;
std_msgs__msg__UInt8 amr_power_msg;
std_msgs__msg__UInt8 msg_amr_set_state;
std_msgs__msg__UInt8 msg_amr_get_state;
std_msgs__msg__UInt8 msg_incoming_heartbeat;
std_msgs__msg__UInt8 msg_outgoing_heartbeat;
std_msgs__msg__Float32MultiArray msg_power_system_actuation_battery_readings;
std_msgs__msg__Int32MultiArray raw_encoder_msg;
std_msgs__msg__UInt16MultiArray motor_error_msg;

MicroROS_Executor_t executor;
MicroROS_Support_t support;
rcl_allocator_t allocator;
MicroROS_Node_t node;
rcl_timer_t encoder_timer;
rcl_timer_t battery_timer;
rcl_timer_t heartbeat_timer;
rcl_timer_t timer_amr_get_state;

wheelControl frontWheel(1, FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                        FRONT_WHEEL_STEER_MOTOR_NODE_ID, 11835, 50);
wheelControl rearLeftWheel(2, REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                           REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID, 5637, 50);
wheelControl rearRightWheel(3, REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                            REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID, 35075, 50);
wheelControl *wheels[] = {&frontWheel, &rearLeftWheel, &rearRightWheel};

uint32_t error_read_time = 0;
uint32_t encoder_read_time = 0;
uint64_t error_counter = 0;
uint32_t battery_read_time = 0;

// Function Definition
void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time);
void callback_timer_power_system_battery_reading(rcl_timer_t *timer,
                                                 int64_t last_call_time);
void callback_timer_amr_get_state(rcl_timer_t *timer, int64_t last_call_time);
void wheel_pose_control_callback(const void *msgin);
void amr_power_callback(const void *msgin);
void callback_amr_set_state(const void *msgin);
void callback_outgoing_heartbeat_timer(rcl_timer_t *timer,
                                       int64_t last_call_time);
void callback_incoming_heartbeat(const void *msgin);

// Error handle loop
void error_loop() {
  Serial.println("uROS Error Has occured!!");
  while (1) {
    delay(50);
    digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
  }
}

void initialize_amr() {
  Serial.println("Starting AMR");
  digitalWrite(MOTOR_SSR, LOW);
  delay(10000);
  frontWheel.m_initializeWheel(true, false);
  rearLeftWheel.m_initializeWheel(false, false);
  rearRightWheel.m_initializeWheel(false, false);
  set_tower_light_state(TOWER_LIGHT_STATE::ACTIVE_MODE);
  Serial.println("AMR Initialized");
}

void amr_engage_brakes() {
  Serial.println("Estop Requested");
  for (auto wheel : wheels) {
    wheel->m_emergencyStopWheel();
  }
}

enum class amrState {
  UNINITIALIZED = 0x00,
  INITIALIZE = 0x01,
  INITIALIZING = 0x02,
  ACTIVE = 0x03,
  ERROR = 0x04,
  UNINITIALIZE = 0x05,
  UNINITIALIZING = 0x06,
};

amrState amr_state = amrState::UNINITIALIZED;
// ------------------------------------------------------ //
// Publishers
// ------------------------------------------------------ //
void setup_all_publishers() {
  INIT_PUB(raw_encoder_value_publisher, node, RAW_ENCODER_PUBLISHER_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Int32MultiArray));
  // Set Encoder publisher
  raw_encoder_msg.data.capacity = 6;
  raw_encoder_msg.data.size = 6;
  raw_encoder_msg.data.data =
      (int32_t *)malloc(sizeof(int32_t) * raw_encoder_msg.data.capacity);
  if (raw_encoder_msg.data.data == NULL) {
    Serial.println("Raw Encoder Message Memory allocation failed!");
  }

  INIT_PUB(motor_error_publisher, node, MOTOR_ERROR_PUBLISHER_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt16MultiArray));
  motor_error_msg.data.capacity = 6;
  motor_error_msg.data.size = 6;
  motor_error_msg.data.data =
      (uint16_t *)malloc(sizeof(uint16_t) * motor_error_msg.data.capacity);
  if (motor_error_msg.data.data == NULL) {
    Serial.println("Motor Error Message Memory allocation failed!");
  }
  for (size_t idx = 0; idx < motor_error_msg.data.size; idx++) {
    motor_error_msg.data.data[idx] = 0x00;
  }

  INIT_PUB(pub_get_actuation_battery_readings, node,
           POWER_SYSTEM_ACTUATION_BATTERY_STATUS_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray));
  msg_power_system_actuation_battery_readings.data.capacity = 3;
  msg_power_system_actuation_battery_readings.data.size = 3;
  msg_power_system_actuation_battery_readings.data.data = (float *)malloc(
      sizeof(float) *
      msg_power_system_actuation_battery_readings.data.capacity);
  if (msg_power_system_actuation_battery_readings.data.data == NULL) {
    Serial.println(
        "Actuation Battery Information Message Memory allocation failed!");
  }

  INIT_PUB(pub_heartbeat, node, HEARTBEAT_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8));
  msg_outgoing_heartbeat.data =
      (uint8_t)ROS_DEVICE_HEARTBEAT::RCB_ROS_HEARTBEAT_ID;

  INIT_PUB(pub_amr_get_state, node, AMR_GET_STATE_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8));
  msg_amr_get_state.data = (uint8_t)amrState::UNINITIALIZED;
  //  RCCHECK(rclc_publisher_init_default(
  //      &calculated_encoder_publisher, &node,
  //      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
  //      CALCULATED_ENCODER_PUBLISHER_TOPIC));
  //
  Serial.println("Publishers Initialization Complete");
}

void setup_all_timers() {
  RCCHECK(rclc_timer_init_default(&encoder_timer, &support.m_support,
                                  RCL_MS_TO_NS(ENCODER_TIME),
                                  encoder_timer_callback));
  RCCHECK(rclc_timer_init_default(&battery_timer, &support.m_support,
                                  RCL_MS_TO_NS(BATTERY_TIME),
                                  callback_timer_power_system_battery_reading));
  RCCHECK(rclc_timer_init_default(&heartbeat_timer, &support.m_support,
                                  RCL_MS_TO_NS(HEARTBEAT_TIME_MS),
                                  callback_outgoing_heartbeat_timer));
  RCCHECK(rclc_timer_init_default(&timer_amr_get_state, &support.m_support,
                                  RCL_MS_TO_NS(AMR_GET_STATE_TIMER_MS),
                                  callback_timer_amr_get_state));
  Serial.println("Timers Initialization Complete");
}

void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time) {
  RCLC_UNUSED(last_call_time);
  if (timer != NULL) {
    PUBLISH(raw_encoder_value_publisher, raw_encoder_msg);

    // TODO: Fetch calculated encoder values here
    //    RCSOFTCHECK(rcl_publish(&calculated_encoder_publisher, &msgout,
    //    NULL));
  }
}

void callback_outgoing_heartbeat_timer(rcl_timer_t *timer,
                                       int64_t last_call_time) {
  RCLC_UNUSED(last_call_time);
  if (timer != NULL) {
    PUBLISH(pub_heartbeat, msg_outgoing_heartbeat);
  }
}

void callback_timer_power_system_battery_reading(rcl_timer_t *timer,
                                                 int64_t last_call_time) {
  RCLC_UNUSED(last_call_time);
  if (timer != NULL) {

    PUBLISH(pub_get_actuation_battery_readings,
            msg_power_system_actuation_battery_readings);
  }
}

void publish_error_msg(void) {
  PUBLISH(motor_error_publisher, motor_error_msg);
}

void callback_timer_amr_get_state(rcl_timer_t *timer, int64_t last_call_time) {
  msg_amr_get_state.data = (uint8_t)amr_state;
  PUBLISH(pub_amr_get_state, msg_amr_get_state);
}

// ------------------------------------------------------ //
// Subscribers
// ------------------------------------------------------ //
void setup_all_subscribers() {
  INIT_SUB(wheel_pose_control_subscriber, executor, node,
           WHEEL_POSE_CONTROL_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
           *wheel_pose_control_callback, wheel_pose_control_msg);
  wheel_pose_control_msg.data.capacity = 6;
  wheel_pose_control_msg.data.size = 6;
  wheel_pose_control_msg.data.data =
      (float *)malloc(sizeof(float) * wheel_pose_control_msg.data.capacity);
  if (wheel_pose_control_msg.data.data == NULL) {
    Serial.println("Memory allocation failed!");
  }

  INIT_SUB(amr_motor_power_subscriber, executor, node, AMR_POWER_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
           *amr_power_callback, amr_power_msg);

  INIT_SUB(sub_heartbeat, executor, node, HEARTBEAT_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
           *callback_incoming_heartbeat, msg_incoming_heartbeat);

  INIT_SUB(sub_amr_set_state, executor, node, AMR_SET_STATE_TOPIC,
           ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8),
           *callback_amr_set_state, msg_amr_set_state);

  Serial.println("Subscribers Initialization Complete");
}
void setup_executor() {
  // create executor
  RCCHECK(rclc_executor_add_timer(&executor.m_executor, &encoder_timer));
  RCCHECK(rclc_executor_add_timer(&executor.m_executor, &battery_timer));
  RCCHECK(rclc_executor_add_timer(&executor.m_executor, &heartbeat_timer));
  RCCHECK(rclc_executor_add_timer(&executor.m_executor, &timer_amr_get_state));
}

void wheel_pose_control_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  wheel_pose_control_msg_recieved = true;
}

void amr_power_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  Serial.println("Emergency Stop Requested");
  amr_engage_brakes();
}

void callback_incoming_heartbeat(const void *msgin) {
  RCLC_UNUSED(msgin);
  if (msg_incoming_heartbeat.data ==
      (uint8_t)ROS_DEVICE_HEARTBEAT::AGX_ORIN_ROS_HEARTBEAT_ID)
    Serial.println("HB");
}

void callback_amr_set_state(const void *msgin) {
  RCLC_UNUSED(msgin);
  flag_state_change_requested = true;
  Serial.print("Requested State: ");
  Serial.println(msg_amr_set_state.data);
}

void callback_battery_message(const CANMessage &message) {
  parse_battery_message(message.id, message.data,
                        msg_power_system_actuation_battery_readings.data.data);
}

byte local_mac[] = {0xAA, 0xBB, 0xCC, 0xEE, 0xDD, 0xFF};
IPAddress local_ip(192, 168, 1, 200);
IPAddress agent_ip(192, 168, 1, 1);
size_t agent_port = 8888;

void setup() {
  // Configure serial transport
  Serial.begin(926100);
  initialize_diagnostic_error_handler();
  pinMode(LED_BUILTIN, OUTPUT);
  pinMode(MOTOR_SSR, OUTPUT);
  pinMode(UR10_SSR, OUTPUT);
  pinMode(ACTUATION_POWER_CONTACTOR_SSR, OUTPUT);
  pinMode(CHARGING_SWITCH_SSR, OUTPUT);
  digitalWrite(MOTOR_SSR, HIGH);
  digitalWrite(ACTUATION_POWER_CONTACTOR_SSR, LOW);
  digitalWrite(UR10_SSR, HIGH);
  digitalWrite(CHARGING_SWITCH_SSR, HIGH);
  set_tower_light_state(TOWER_LIGHT_STATE::UNINITIALIZED_MODE);
  set_tower_light_state(TOWER_LIGHT_STATE::INITIALIZING_MODE);

  // Setup uROS
  INIT_UROS(local_mac, local_ip, agent_ip, agent_port, allocator, support);
  INIT_NODE(node, NODE_NAME, support);
  INIT_EXECUTOR(executor, support, TOTAL_PUB_SUB_HANDLES, allocator);

  Serial.println("ROS Enable in Process!");

  setup_all_publishers();
  setup_all_subscribers();
  setup_all_timers();
  setup_executor();

  Serial.println("ROS Enable Complete!");
  // Setup AMR Motors
  ACAN_T4_Settings can1Settings(1000 * 1000);
  ACAN_T4_Settings can2Settings(250 * 1000);
  uint32_t errorCode1 = ACAN_T4::can1.begin(can1Settings);
  uint32_t errorCode2 = ACAN_T4::can2.begin(can2Settings);
  if (0 == errorCode1) {
    Serial.println("can1 ok");
  } else {
    Serial.print("Error can1: 0x");
    Serial.println(errorCode1, HEX);
  }
  if (0 == errorCode2) {
    Serial.println("can2 ok");
  } else {
    Serial.print("Error can2: 0x");
    Serial.println(errorCode2, HEX);
  }

  digitalWrite(LED_BUILTIN, HIGH);
  Serial.println("CAN Initialization Complete!");
  Serial.println("Ready to Use!");
  set_tower_light_state(TOWER_LIGHT_STATE::INITIALIZED_MODE);
}

void amr_active_state() {
  if (wheel_pose_control_msg_recieved == true) {
    for (uint8_t idx = 0; idx < 3; idx++) {
      float travel_motor_speed_rpm =
          AMR_LINEAR_VELOCITY_MpS_TO_ANGULAR_VELOCITY_RPM(
              wheel_pose_control_msg.data.data[2 * idx]);
      float steer_angle_degrees =
          wheel_pose_control_msg.data.data[(2 * idx) + 1];
      wheels[idx]->m_setTravelSpeed_RPM(travel_motor_speed_rpm);
      wheels[idx]->m_setAbsoluteSteerToAngle_Degrees(steer_angle_degrees);
    }
    wheel_pose_control_msg_recieved = false;
  }

  if (millis() - encoder_read_time > ENCODER_TIME) {
    for (auto wheel : wheels) {
      wheel->m_requestEncoderReadings();
    }
    encoder_read_time = millis();
  }
  if (millis() - error_read_time > ERROR_TIME) {
    for (auto wheel : wheels) {
      wheel->m_requestErrorReadings();
    }
    error_read_time = millis();
  }

  if (ACAN_T4::can1.available()) {
    // Receive the message
    CANMessage message;
    ACAN_T4::can1.receive(message);
    const uint16_t message_idx =
        (uint16_t)interpret_can_bytes_to_decimal_big_endian(
            1.0f, 0.0f, 2, message.data[1], message.data[2]);

    switch (message.id) {
    case RECIEVE_MESSAGE_CAN_IDENTIFIER + FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case RECIEVE_MESSAGE_CAN_IDENTIFIER + FRONT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (frontWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[0], &motor_error_msg.data.data[1]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        frontWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[0],
            &raw_encoder_msg.data.data[1]);
      }
      break;

    case RECIEVE_MESSAGE_CAN_IDENTIFIER + REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case RECIEVE_MESSAGE_CAN_IDENTIFIER + REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (rearLeftWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[2], &motor_error_msg.data.data[3]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        rearLeftWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[2],
            &raw_encoder_msg.data.data[3]);
      }
      break;

    case RECIEVE_MESSAGE_CAN_IDENTIFIER + REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case RECIEVE_MESSAGE_CAN_IDENTIFIER + REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (rearRightWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[4], &motor_error_msg.data.data[5]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        rearRightWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[4],
            &raw_encoder_msg.data.data[5]);
      }
      break;

    default:
      //      Serial.print("ID: ");
      //      Serial.println(message.id, HEX);
      //      Serial.print("HEX Message: ");
      //      for (int i = 0; i < 8; i++) {
      //        Serial.print(message.data[i], HEX);
      //        Serial.print(" ");
      //      }
      //      Serial.println();
      break;
    }
  }

  // AMR Error handling
  for (size_t idx = 0; idx < motor_error_msg.data.size; idx++) {
    if (motor_error_msg.data.data[idx] > 0) {
      amr_engage_brakes();
      digitalWrite(MOTOR_SSR, HIGH);
      for (size_t idx = 0; idx < motor_error_msg.data.size; idx++) {
        motor_error_msg.data.data[idx] = 0x00;
      }
      error_counter++;
      Serial.print("Error Detected. Error Count: ");
      Serial.println(error_counter);
      amr_state = amrState::ERROR;
      set_tower_light_state(TOWER_LIGHT_STATE::ERROR_MODE);
      break;
    }
  }
}

void loop() {
  // User Control
  if (flag_state_change_requested) {
    switch ((amrState)msg_amr_set_state.data) {
    case amrState::INITIALIZE:
      amr_state = amrState::INITIALIZING;
      break;
    case amrState::UNINITIALIZE:
      amr_state = amrState::UNINITIALIZING;
      break;
    default:
      Serial.print("Invalid State Requested By User: ");
      Serial.println((uint8_t)msg_amr_set_state.data);
      break;
    }
    flag_state_change_requested = false;
  }

  // State Machine for AMR Control
  switch (amr_state) {
  case amrState::UNINITIALIZED:
    break;
  case amrState::INITIALIZE:
    break;
  case amrState::INITIALIZING:
    initialize_amr();
    amr_state = amrState::ACTIVE;
    break;
  case amrState::ACTIVE:
    amr_active_state();
    break;
  case amrState::ERROR:
    break;
  case amrState::UNINITIALIZE:
    break;
  case amrState::UNINITIALIZING:
    set_tower_light_state(TOWER_LIGHT_STATE::INITIALIZING_MODE);
    amr_engage_brakes();
    digitalWrite(MOTOR_SSR, HIGH);
    amr_state = amrState::UNINITIALIZED;
    break;
  default:
    Serial.print("Invalid State: ");
    Serial.println((uint8_t)amr_state);
    break;
  }

  if (ACAN_T4::can2.available()) {
    // Receive the message
    CANMessage message;
    ACAN_T4::can2.receive(message);
    parse_battery_message(
        message.id, message.data,
        msg_power_system_actuation_battery_readings.data.data);
  }

#if BATTERY_BMS == DALYBMS
  if (millis() - battery_read_time > BATTERY_TIME) {
    request_battery_information(ACTUATION_SYSTEM_BATTERY_BMS_ADDRESS);
    battery_read_time = millis();
  }
#endif
  SPIN_EXECUTOR(executor, 1);
}

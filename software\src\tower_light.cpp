#include "tower_light.hpp"
#include "core_pins.h"
#include "rcb_pinout.hpp"
#include <cstdint>

// Scoped only to the File
enum class TOWER_LIGHT_PIN_STATE { OFF = 0x00, ON = 0x01, BLINKING = 0x02 };

static void set_tower_light_pin_state(TOWER_LIGHT_PIN pin,
                                      TOWER_LIGHT_PIN_STATE pin_state) {
  switch (pin_state) {
  case TOWER_LIGHT_PIN_STATE::OFF:
    digitalWrite(static_cast<uint8_t>(pin),
                 static_cast<uint8_t>(TOWER_LIGHT_PIN_STATE::OFF));
    break;

  case TOWER_LIGHT_PIN_STATE::ON:
    digitalWrite(static_cast<uint8_t>(pin),
                 static_cast<uint8_t>(TOWER_LIGHT_PIN_STATE::ON));
    break;
  case TOWER_LIGHT_PIN_STATE::BLINKING:
    Serial.println("[TOWER LIGHT] BLINKING State not programmed");
    break;
  default:
    Serial.println("[TOWER LIGHT] Pin State Unknown");
  }
}

static void set_tower_lights(TOWER_LIGHT_PIN_STATE red_light_state,
                             TOWER_LIGHT_PIN_STATE buzzer_state,
                             TOWER_LIGHT_PIN_STATE yellow_light_state,
                             TOWER_LIGHT_PIN_STATE green_light_state,
                             TOWER_LIGHT_PIN_STATE blue_light_state) {
  set_tower_light_pin_state(TOWER_LIGHT_PIN::RED, red_light_state);
  set_tower_light_pin_state(TOWER_LIGHT_PIN::BUZZER, buzzer_state);
  set_tower_light_pin_state(TOWER_LIGHT_PIN::YELLOW, yellow_light_state);
  set_tower_light_pin_state(TOWER_LIGHT_PIN::GREEN, green_light_state);
  set_tower_light_pin_state(TOWER_LIGHT_PIN::BLUE, blue_light_state);
}

void set_tower_light_state(TOWER_LIGHT_STATE robot_state) {
  switch (robot_state) {

  case TOWER_LIGHT_STATE::INITIALIZING_MODE:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::ON, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::ON, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF);
    break;

  case TOWER_LIGHT_STATE::INITIALIZED_MODE:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::ON, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF);

    break;
  case TOWER_LIGHT_STATE::WIFI_CONNECTING:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::BLINKING,
                     TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF);
    break;

  case TOWER_LIGHT_STATE::ACTIVE_MODE:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::ON,
                     TOWER_LIGHT_PIN_STATE::OFF);
    break;

    // TODO: Switch from GREEN TO BLUE Light When the New Tower Light arrive
  case TOWER_LIGHT_STATE::AUTONOMOUS_MODE:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::ON,
                     TOWER_LIGHT_PIN_STATE::ON);
    break;

  case TOWER_LIGHT_STATE::ERROR_MODE:
    set_tower_lights(TOWER_LIGHT_PIN_STATE::ON, TOWER_LIGHT_PIN_STATE::ON,
                     TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF);
    break;

    // Kept at last since this gets called only once
  case TOWER_LIGHT_STATE::UNINITIALIZED_MODE:
    pinMode(static_cast<uint8_t>(TOWER_LIGHT_PIN::RED), OUTPUT);
    pinMode(static_cast<uint8_t>(TOWER_LIGHT_PIN::YELLOW), OUTPUT);
    pinMode(static_cast<uint8_t>(TOWER_LIGHT_PIN::GREEN), OUTPUT);
    pinMode(static_cast<uint8_t>(TOWER_LIGHT_PIN::BLUE), OUTPUT);
    pinMode(static_cast<uint8_t>(TOWER_LIGHT_PIN::BUZZER), OUTPUT);
    set_tower_lights(TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF, TOWER_LIGHT_PIN_STATE::OFF,
                     TOWER_LIGHT_PIN_STATE::OFF);
    break;

  default:
    Serial.println("[TOWER LIGHT]: Unknown Tower Light State Called");
    break;
  }
}

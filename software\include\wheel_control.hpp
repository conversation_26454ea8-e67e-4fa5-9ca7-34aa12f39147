#ifndef WHEEL_CONTROL_HPP
#define WHEEL_CONTROL_HPP
#include "config.hpp"
#include "object_dictionary.hpp"
#include <ACAN_T4.h>
#include <cfloat>
#include <stdint.h>

// CAN Specific configurations
#define CAN_MSG_DELAY 10.0f

class wheelControl {
public:
  wheelControl(const uint8_t wheelID, const uint8_t travelMotorID,
               const uint8_t steerMotorID,
               const int32_t steeringEncoderOffset_Decimal,
               const uint16_t heatbeatTime_ms);
  ~wheelControl();
  void m_initializeWheel(const bool invertTravelMotorDirection,
                         const bool invertSteeringMotorDirection);

  void m_enableTravelMotor(bool enable);
  void m_enableSteerMotor(bool enable);
  void m_setProfileSpeedForSteerMotor_RPM(const float profileSpeed_RPM);
  void
  m_setProfileAccelerationForTravelMotor_RPS(const float profileAccel_RPS,
                                             const float profileDeaccel_RPS);
  void
  m_setProfileAccelerationForSteerMotor_RPS(const float profileAccel_RPS,
                                            const float profileDeaccel_RPS);
  void
  m_setAbsoluteSteerToAngle_Degrees(const float absoluteSteeringAngle_Degrees);
  void m_setTravelSpeed_RPM(const float travelSpeed);

  void m_setNodeHeartbeatTime(const uint8_t nodeID,
                              const uint16_t heartBeatTime_ms);

  void m_emergencyStopWheel();
  void m_requestErrorReadings();
  bool m_parseErrorReadings(const uint16_t nodeID, const uint8_t LSB,
                            const uint8_t MSB, uint16_t *const travelMotorError,
                            uint16_t *const steerMotorError);
  void m_requestEncoderReadings();
  void m_parseEncoderReading(const uint16_t nodeID, const uint8_t message1,
                             const uint8_t message2, const uint8_t message3,
                             const uint8_t message4,
                             int32_t *const travelMotorReading,
                             int32_t *const steerMotorReading);

private:
  // For both motors
  const uint8_t m_wheelID;
  const uint16_t m_heartBeatTime_ms;

  // For Travel Motor
  const uint8_t m_travelMotorNodeID;
  const float m_travel_pos_vel_conversion_factor =
      (512.0f * TRAVEL_ENCODER_RESOLUTION * TRAVEL_MOTOR_GEAR_RATIO) / 1875.0;
  const float m_travel_acc_dec_conversion_factor =
      (65536.0f * TRAVEL_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
  float m_currentTravelSpeed_RPM = 0.0f;
  uint8_t m_travelMotor_Direction = INVERT_DIR_CW;

  // For Steer Motor
  const uint8_t m_steerMotorNodeID;
  const float m_steering_pos_vel_conversion_factor =
      512.0f * STEERING_ENCODER_RESOLUTION / 1875.0;
  const float m_steering_acc_dec_conversion_factor =
      (65536.0f * STEERING_ENCODER_RESOLUTION / 1000.0f) / 4000.0f;
  const int32_t m_steeringEncoderOffset_Decimal;
  float m_currentSteeringAngle_deg = FLT_MAX;
  int8_t m_invertSteering = 1;

  enum class m_dataType { U8, U16, U32, I8, I16, I32 };
  enum class m_operationType { DOWNLOAD, SDO_READ };

  const CANMessage
  m_generateMessage(const m_operationType operationType, const uint8_t nodeID,
                    const m_dataType dataType, const uint16_t index,
                    const uint8_t subIndex, const uint32_t data);
  void m_setupTravelMotor(bool invertDirection);
  void m_setupSteeringMotor();
};
#endif // WHEEL_CONTROL_HPP

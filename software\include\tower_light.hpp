#pragma once

#include <Arduino.h>
#include <cstdint>

enum class TOWER_LIGHT_STATE {
  UNINITIALIZED_MODE, // ALL OFF
  INITIALIZING_MODE,  // Yellow and RED ON
  INITIALIZED_MODE,   // Only Yellow ON
  WIFI_CONNECTING,    // Yellow Blinking
  ACTIVE_MODE,        // Only Green ON
  AUTONOMOUS_MODE,    // Green + Blue ON
  ERROR_MODE          // Only RED ON
};

void set_tower_light_state(TOWER_LIGHT_STATE robot_state);

#pragma once
#include "config.hpp"
#include <stdbool.h>
#include <stdint.h>

#define JKBMS 1
#define DALYBMS 2

#if BATTERY_BMS == JKBMS
#include "battery_jkbms.hpp"
#elif BATTERY_BMS == DALYBMS
#include "battery_dalybms.hpp"
#else
#error "Battery BMS Not Defined"
#endif // BATTERY_BMS

void parse_battery_message(const uint32_t can_id, const uint8_t message[8],
                           float msg_actuation_battery_ros[3]);

FROM ros:humble
ARG USERNAME="10xu"
ENV USERNAME=${USERNAME}

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
      make python3 python3-dev python3-pip python3-venv git cmake curl udev sudo && \
    rm -rf /var/lib/apt/lists/*

RUN useradd -m -s /bin/bash ${USERNAME} -p "" \
    && usermod -a -G sudo,dialout,plugdev ${USERNAME} \
    && echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/${USERNAME} \
    && chmod 0440 /etc/sudoers.d/${USERNAME}


RUN curl -fsSL https://raw.githubusercontent.com/platformio/platformio-core/develop/platformio/assets/system/99-platformio-udev.rules \
    | tee /etc/udev/rules.d/99-platformio-udev.rules

RUN chmod +rw /dev/tty*
USER ${USERNAME}

WORKDIR /home/<USER>
RUN curl -fsSL -o get-platformio.py https://raw.githubusercontent.com/platformio/platformio-core-installer/master/get-platformio.py
RUN python3 get-platformio.py \
    && rm get-platformio.py
ENV PATH="$PATH:/home/<USER>/.platformio/penv/bin"

COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN sudo chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["/bin/bash", "/usr/local/bin/entrypoint.sh"]


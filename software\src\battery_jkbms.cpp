#include "utils.hpp"
#include <battery_jkbms.hpp>

void read_battery_status_1(const uint8_t message[8], float *battery_voltage,
                           float *battery_current, float *soc) {
  *battery_voltage = interpret_can_bytes_to_decimal_big_endian(
      0.1f, 0, 2, message[0], message[1]);
  *battery_current = interpret_can_bytes_to_decimal_big_endian(
      0.1f, -400, 2, message[2], message[3]);
  *soc = interpret_can_bytes_to_decimal_big_endian(1.0f, 0, 1, message[4]);
}

void read_battery_status_2(const uint8_t message[8]) {
  const float remaining_capacity = interpret_can_bytes_to_decimal_big_endian(
      0.1f, 0, 2, message[0], message[1]);
  const float battery_capacity = interpret_can_bytes_to_decimal_big_endian(
      0.1f, -0, 2, message[2], message[3]);
  const float total_cycle_capacity = interpret_can_bytes_to_decimal_big_endian(
      0.1f, 0, 2, message[4], message[5]);
  const float battery_cycle_count = interpret_can_bytes_to_decimal_big_endian(
      1.0f, 0, 2, message[6], message[7]);
}

void read_cell_voltage(const uint8_t message[8]) {
  const float max_cell_voltage = interpret_can_bytes_to_decimal_big_endian(
      1.0f, 0, 2, message[0], message[1]);
  const float max_cell_voltage_id =
      interpret_can_bytes_to_decimal_big_endian(1.0f, 1.0f, 1, message[2]);
  const float min_cell_voltage = interpret_can_bytes_to_decimal_big_endian(
      1.0f, 0, 2, message[3], message[4]);
  const float min_cell_voltage_id =
      interpret_can_bytes_to_decimal_big_endian(1.0f, 1.0f, 1, message[5]);
}

void read_cell_temperature(const uint8_t message[8]) {
  const float max_cell_temperature =
      interpret_can_bytes_to_decimal_big_endian(1.0f, -50, 1, message[0]);
  const float max_cell_temperature_id =
      interpret_can_bytes_to_decimal_big_endian(1.0f, 1.0f, 1, message[1]);
  const float min_cell_temperature =
      interpret_can_bytes_to_decimal_big_endian(1.0f, -50, 1, message[2]);
  const float min_cell_temperature_id =
      interpret_can_bytes_to_decimal_big_endian(1.0f, 1.0f, 1, message[3]);
  const float average_cell_temperature =
      interpret_can_bytes_to_decimal_big_endian(1.0f, -50, 1, message[4]);
}

void read_bms_information(const uint8_t message[8]) {
  const float bms_runtime = interpret_can_bytes_to_decimal_big_endian(
      1.0f, 0, 4, message[0], message[1], message[2], message[3]);
  const float heating_current = interpret_can_bytes_to_decimal_big_endian(
      1.0f, 0, 2, message[4], message[5]);
  const float battery_health =
      interpret_can_bytes_to_decimal_big_endian(1.0f, 0, 1, message[6]);
}

void read_switch_status(const uint8_t message) {
  /**
   * bits[0] = 0 -> Charging MOSFET Off
   * bits[0] = 1 -> Charging MOSFET On
   * bits[1] = 0 -> Discharge MOSFET Off
   * bits[1] = 1 -> Discharge MOSFET On
   * bits[2] = 0 -> Unequilibrium
   * bits[2] = 1 -> In equilibrium
   * bits[3] = 0 -> Heating MOSFET Disconnected
   * bits[3] = 1 -> Heating MOSFET Connected
   * bits[4] = 0 -> Charger Unplugged
   * bits[4] = 1 -> Charger Plugged
   * bits[5] = 0 -> ACC Status Off
   * bits[5] = 1 -> ACC Status On
   */
  unsigned char bits[6];
  for (int i = 0; i < 6; i++) {
    bits[i] = (message >> i) & 0x01;
  }
}

void read_control_information(const uint8_t message[8]) {
  /**
   * bits[0] = 0 -> Charging control off
   * bits[0] = 1 -> Charging control on
   * bits[1] = 0 -> Discharging control off
   * bits[1] = 1 -> Discharging control on
   * bits[2] = 0 -> Balance control off
   * bits[2] = 1 -> Balance control on
   *
   */
  unsigned char bits[3];
  for (int i = 0; i < 3; i++) {
    bits[i] = (message[0] >> i) & 0x01;
  }
  bool charging_switch = message[4];
  bool discharge_switch = message[5];
  bool balance_switch = message[6];
}

void read_charging_request(const uint8_t message[8]) {
  float charging_voltage = interpret_can_bytes_to_decimal_little_endian(
      0.1f, 0, 2, message[0], message[1]);
  float charging_current = interpret_can_bytes_to_decimal_little_endian(
      0.1f, 0, 2, message[2], message[3]);
  bool charger_switch_status = message[4];
  bool charging_mode_on = message[5];
}
